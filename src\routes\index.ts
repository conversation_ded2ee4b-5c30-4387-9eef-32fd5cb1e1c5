import { Router } from 'express';
import authRoutes from './auth';
import userRoutes from './user';
import dashboardRoutes from './dashboard';
import categoryRoutes from './categoryRoutes';
import { createTransactionRoutes } from './transactions';
import {
  incomeController,
  needController,
  wantController,
  investmentController,
  donationController,
} from '../controllers';

const router = Router();

// Auth routes
router.use('/auth', authRoutes);

// User routes
router.use('/user', userRoutes);

// Dashboard routes
router.use('/dashboard', dashboardRoutes);

// Category routes
router.use('/categories', categoryRoutes);

// Transaction routes for each category
router.use('/incomes', createTransactionRoutes(incomeController));
router.use('/needs', createTransactionRoutes(needController));
router.use('/wants', createTransactionRoutes(wantController));
router.use('/investments', createTransactionRoutes(investmentController));
router.use('/donations', createTransactionRoutes(donationController));

export default router;
