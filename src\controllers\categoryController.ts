import { Response } from 'express';
import { z } from 'zod';
import { Category, Transaction } from '../models';
import { AuthenticatedRequest } from '../middlewares/auth';

// Validation schemas
const createCategorySchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(30, 'Name must be less than 30 characters')
    .regex(/^[a-z0-9_]+$/, 'Name can only contain lowercase letters, numbers, and underscores'),
  displayName: z.string()
    .min(1, 'Display name is required')
    .max(50, 'Display name must be less than 50 characters'),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Color must be a valid hex color'),
  icon: z.string()
    .min(1, 'Icon is required'),
  percentage: z.number()
    .min(0, 'Percentage must be at least 0')
    .max(100, 'Percentage cannot exceed 100'),
});

const updateCategorySchema = createCategorySchema.partial();

export class CategoryController {
  // Get all categories for the authenticated user
  static async getAll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {


      if (!req.user) {
        console.log('No user found in request');
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      let categories = await Category.find({ userId: req.user.id })
        .sort({ order: 1 })
        .exec();

      // If user has no categories, initialize default ones
      if (categories.length === 0) {
        console.log('No categories found for user, initializing defaults...');
        try {
          await CategoryController.initializeDefaultCategories(req.user.id);
          categories = await Category.find({ userId: req.user.id })
            .sort({ order: 1 })
            .exec();
          console.log(`Initialized ${categories.length} default categories for user`);
        } catch (initError) {
          console.error('Failed to initialize default categories:', initError);
          // Continue with empty categories rather than failing
        }
      }

      const response = {
        data: categories,
        total: categories.length,
        maxAllowed: parseInt(process.env.MAX_CATEGORIES_PER_USER || '5'),
      };


      res.json(response);
    } catch (error) {
      console.error('Error fetching categories:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Get a specific category by ID
  static async getById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const category = await Category.findOne({
        _id: req.params.id,
        userId: req.user.id,
      });

      if (!category) {
        res.status(404).json({ error: 'Category not found' });
        return;
      }

      res.json({ data: category });
    } catch (error) {
      console.error('Error fetching category:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Create a new category
  static async create(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Validate request body
      const validationResult = createCategorySchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          error: 'Validation failed',
          details: validationResult.error.errors,
        });
        return;
      }

      const { name, displayName, color, icon, percentage } = validationResult.data;

      // Check if category name already exists for this user
      const existingCategory = await Category.findOne({
        userId: req.user.id,
        name: name,
      });

      if (existingCategory) {
        res.status(400).json({ error: 'Category name already exists' });
        return;
      }

      // Get the next order number
      const lastCategory = await Category.findOne({ userId: req.user.id })
        .sort({ order: -1 })
        .exec();
      const nextOrder = lastCategory ? lastCategory.order + 1 : 0;

      // Create the category
      const category = new Category({
        userId: req.user.id,
        name,
        displayName,
        color,
        icon,
        percentage,
        isDefault: false,
        order: nextOrder,
      });

      await category.save();

      res.status(201).json({
        message: 'Category created successfully',
        data: category,
      });
    } catch (error: any) {
      console.error('Error creating category:', error);
      
      if (error.message.includes('Maximum') && error.message.includes('categories allowed')) {
        res.status(400).json({ error: error.message });
        return;
      }
      
      if (error.message.includes('Total category percentages')) {
        res.status(400).json({ error: error.message });
        return;
      }

      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Update an existing category
  static async update(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Validate request body
      const validationResult = updateCategorySchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          error: 'Validation failed',
          details: validationResult.error.errors,
        });
        return;
      }

      const updateData = validationResult.data;

      // Check if category exists and belongs to user
      const category = await Category.findOne({
        _id: req.params.id,
        userId: req.user.id,
      });

      if (!category) {
        res.status(404).json({ error: 'Category not found' });
        return;
      }

      // If updating name, check for duplicates
      if (updateData.name && updateData.name !== category.name) {
        const existingCategory = await Category.findOne({
          userId: req.user.id,
          name: updateData.name,
          _id: { $ne: category._id },
        });

        if (existingCategory) {
          res.status(400).json({ error: 'Category name already exists' });
          return;
        }
      }

      // Update the category
      Object.assign(category, updateData);
      await category.save();

      res.json({
        message: 'Category updated successfully',
        data: category,
      });
    } catch (error: any) {
      console.error('Error updating category:', error);
      
      if (error.message.includes('Total category percentages')) {
        res.status(400).json({ error: error.message });
        return;
      }

      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Delete a category
  static async delete(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const category = await Category.findOne({
        _id: req.params.id,
        userId: req.user.id,
      });

      if (!category) {
        res.status(404).json({ error: 'Category not found' });
        return;
      }

      // Check if category has transactions
      const transactionCount = await Transaction.countDocuments({
        categoryId: category._id,
      });

      if (transactionCount > 0) {
        res.status(400).json({
          error: 'Cannot delete category with existing transactions',
          transactionCount,
        });
        return;
      }

      await Category.findByIdAndDelete(category._id);

      res.json({
        message: 'Category deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Reorder categories
  static async reorder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { categoryIds } = req.body;

      if (!Array.isArray(categoryIds)) {
        res.status(400).json({ error: 'categoryIds must be an array' });
        return;
      }

      // Update order for each category
      const updatePromises = categoryIds.map((categoryId: string, index: number) =>
        Category.findOneAndUpdate(
          { _id: categoryId, userId: req.user!.id },
          { order: index },
          { new: true }
        )
      );

      await Promise.all(updatePromises);

      res.json({
        message: 'Categories reordered successfully',
      });
    } catch (error) {
      console.error('Error reordering categories:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Initialize default categories for a new user
  static async initializeDefaultCategories(userId: string): Promise<void> {
    try {
      const defaultCategories = (Category as any).getDefaultCategories();

      const categories = defaultCategories.map((cat: any) => ({
        ...cat,
        userId,
      }));

      await Category.insertMany(categories);
      console.log(`Successfully initialized ${categories.length} default categories for user ${userId}`);
    } catch (error) {
      console.error('Error initializing default categories:', error);
      throw error;
    }
  }
}
