import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/authService';
import { User } from '../models';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    deviceId: string;
  };
}

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | null = null;

    // Try to get token from Authorization header first
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    // If no Bearer token, try to get from cookies (for frontend API calls)
    if (!token && req.cookies?.accessToken) {
      token = req.cookies.accessToken;
    }

    if (!token) {
      res.status(401).json({ error: 'Access token required' });
      return;
    }

    const payload = AuthService.verifyAccessToken(token);

    // Verify user and device still exist
    const user = await User.findById(payload.userId);
    if (!user) {
      res.status(401).json({ error: 'User not found' });
      return;
    }

    const device = user.devices.find(d => d.deviceId === payload.deviceId);
    if (!device) {
      res.status(401).json({ error: 'Device not authorized' });
      return;
    }

    // Update last seen
    device.lastSeen = new Date();
    await user.save();

    req.user = {
      id: payload.userId,
      deviceId: payload.deviceId,
    };

    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid or expired token' });
  }
};

// Alias for backward compatibility
export const requireAuth = authenticate;
