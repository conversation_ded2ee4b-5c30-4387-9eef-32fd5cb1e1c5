import { Income, Need, Want, Investment, Donation, Category, Transaction } from '../models';
import { calculateTotal } from '../utils/helpers';

export interface BudgetAllocation {
  needs: number;
  wants: number;
  investments: number;
  donations: number;
}

export interface DynamicBudgetAllocation {
  [categoryName: string]: number;
}

export interface CategorySummary {
  allocated: number;
  spent: number;
  remaining: number;
  percentUsed: number;
  isOverBudget: boolean;
}

export interface DashboardData {
  totalIncome: number;
  budgetAllocation: BudgetAllocation;
  summary: {
    needs: CategorySummary;
    wants: CategorySummary;
    investments: CategorySummary;
    donations: CategorySummary;
  };
  totalSpent: number;
  totalRemaining: number;
  overallBudgetStatus: 'under' | 'over' | 'exact';
}

export interface DynamicDashboardData {
  totalIncome: number;
  budgetAllocation: DynamicBudgetAllocation;
  summary: {
    [categoryName: string]: CategorySummary & {
      categoryId: string;
      displayName: string;
      color: string;
      icon: string;
    };
  };
  totalSpent: number;
  totalRemaining: number;
  overallBudgetStatus: 'under' | 'over' | 'exact';
  categories: Array<{
    _id: string;
    name: string;
    displayName: string;
    color: string;
    icon: string;
    percentage: number;
    order: number;
  }>;
}

export class DashboardService {
  // Budget allocation percentages
  private static readonly ALLOCATION_PERCENTAGES = {
    needs: 0.25,      // 25%
    wants: 0.25,      // 25%
    investments: 0.45, // 45%
    donations: 0.05,   // 5%
  };

  static async getDashboardData(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<DashboardData> {
    // Build date filter
    const dateFilter: any = { userId };
    if (startDate || endDate) {
      dateFilter.date = {};
      if (startDate) dateFilter.date.$gte = startDate;
      if (endDate) dateFilter.date.$lte = endDate;
    }

    // Get all data in parallel
    const [incomes, needs, wants, investments, donations] = await Promise.all([
      Income.find(dateFilter),
      Need.find(dateFilter),
      Want.find(dateFilter),
      Investment.find(dateFilter),
      Donation.find(dateFilter),
    ]);

    // Calculate totals
    const totalIncome = calculateTotal(incomes);
    const totalNeeds = calculateTotal(needs);
    const totalWants = calculateTotal(wants);
    const totalInvestments = calculateTotal(investments);
    const totalDonations = calculateTotal(donations);

    // Calculate budget allocations
    const budgetAllocation: BudgetAllocation = {
      needs: totalIncome * this.ALLOCATION_PERCENTAGES.needs,
      wants: totalIncome * this.ALLOCATION_PERCENTAGES.wants,
      investments: totalIncome * this.ALLOCATION_PERCENTAGES.investments,
      donations: totalIncome * this.ALLOCATION_PERCENTAGES.donations,
    };

    // Calculate category summaries
    const summary = {
      needs: this.calculateCategorySummary(budgetAllocation.needs, totalNeeds),
      wants: this.calculateCategorySummary(budgetAllocation.wants, totalWants),
      investments: this.calculateCategorySummary(budgetAllocation.investments, totalInvestments),
      donations: this.calculateCategorySummary(budgetAllocation.donations, totalDonations),
    };

    // Calculate overall totals
    const totalSpent = totalNeeds + totalWants + totalInvestments + totalDonations;
    const totalAllocated = Object.values(budgetAllocation).reduce((sum, amount) => sum + amount, 0);
    const totalRemaining = totalAllocated - totalSpent;

    // Determine overall budget status
    let overallBudgetStatus: 'under' | 'over' | 'exact' = 'exact';
    if (totalSpent > totalAllocated) {
      overallBudgetStatus = 'over';
    } else if (totalSpent < totalAllocated) {
      overallBudgetStatus = 'under';
    }

    return {
      totalIncome,
      budgetAllocation,
      summary,
      totalSpent,
      totalRemaining,
      overallBudgetStatus,
    };
  }

  private static calculateCategorySummary(allocated: number, spent: number): CategorySummary {
    const remaining = allocated - spent;
    const percentUsed = allocated > 0 ? (spent / allocated) * 100 : 0;
    const isOverBudget = spent > allocated;

    return {
      allocated: Math.round(allocated * 100) / 100, // Round to 2 decimal places
      spent: Math.round(spent * 100) / 100,
      remaining: Math.round(remaining * 100) / 100,
      percentUsed: Math.round(percentUsed * 100) / 100,
      isOverBudget,
    };
  }

  static async getMonthlyTrends(userId: string, months: number = 6): Promise<any[]> {
    const trendsData = [];
    const currentDate = new Date();

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(currentDate);
      date.setMonth(date.getMonth() - i);

      // Set date range for the month
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);

      const dateFilter = {
        userId,
        date: {
          $gte: startOfMonth,
          $lte: endOfMonth
        }
      };

      // Get all data for this month in parallel
      const [incomes, needs, wants, investments, donations] = await Promise.all([
        Income.find(dateFilter),
        Need.find(dateFilter),
        Want.find(dateFilter),
        Investment.find(dateFilter),
        Donation.find(dateFilter),
      ]);

      // Calculate totals
      const totalIncome = incomes.reduce((sum, item) => sum + item.amount, 0);
      const totalNeeds = needs.reduce((sum, item) => sum + item.amount, 0);
      const totalWants = wants.reduce((sum, item) => sum + item.amount, 0);
      const totalInvestments = investments.reduce((sum, item) => sum + item.amount, 0);
      const totalDonations = donations.reduce((sum, item) => sum + item.amount, 0);

      trendsData.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        income: totalIncome,
        needs: totalNeeds,
        wants: totalWants,
        investments: totalInvestments,
        donations: totalDonations,
      });
    }

    return trendsData;
  }

  // New method for dynamic categories
  static async getDynamicDashboardData(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<DynamicDashboardData> {
    // Build date filter
    const dateFilter: any = { userId };
    if (startDate || endDate) {
      dateFilter.date = {};
      if (startDate) dateFilter.date.$gte = startDate;
      if (endDate) dateFilter.date.$lte = endDate;
    }

    // Get user's categories
    const categories = await Category.find({ userId }).sort({ order: 1 });

    if (categories.length === 0) {
      throw new Error('No categories found for user');
    }

    // Get income category (should have 0% allocation)
    const incomeCategory = categories.find(cat => cat.name === 'income');
    const budgetCategories = categories.filter(cat => cat.name !== 'income');

    // Get all transactions
    const transactions = await Transaction.find(dateFilter).populate('category');

    // Calculate total income
    const incomeTransactions = incomeCategory
      ? transactions.filter(t => t.categoryId.toString() === incomeCategory._id.toString())
      : [];
    const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);

    // Calculate budget allocation based on user's percentages
    const budgetAllocation: DynamicBudgetAllocation = {};
    budgetCategories.forEach(category => {
      budgetAllocation[category.name] = (totalIncome * category.percentage) / 100;
    });

    // Calculate spending by category
    const summary: DynamicDashboardData['summary'] = {};
    let totalSpent = 0;

    budgetCategories.forEach(category => {
      const categoryTransactions = transactions.filter(
        t => t.categoryId.toString() === category._id.toString()
      );
      const spent = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
      const allocated = budgetAllocation[category.name];

      totalSpent += spent;

      summary[category.name] = {
        ...this.calculateCategorySummary(allocated, spent),
        categoryId: category._id.toString(),
        displayName: category.displayName,
        color: category.color,
        icon: category.icon,
      };
    });

    const totalAllocated = Object.values(budgetAllocation).reduce((sum, amount) => sum + amount, 0);
    const totalRemaining = totalAllocated - totalSpent;

    let overallBudgetStatus: 'under' | 'over' | 'exact' = 'exact';
    if (totalSpent < totalAllocated) {
      overallBudgetStatus = 'under';
    } else if (totalSpent > totalAllocated) {
      overallBudgetStatus = 'over';
    }

    return {
      totalIncome: Math.round(totalIncome * 100) / 100,
      budgetAllocation,
      summary,
      totalSpent: Math.round(totalSpent * 100) / 100,
      totalRemaining: Math.round(totalRemaining * 100) / 100,
      overallBudgetStatus,
      categories: categories.map(cat => ({
        _id: cat._id.toString(),
        name: cat.name,
        displayName: cat.displayName,
        color: cat.color,
        icon: cat.icon,
        percentage: cat.percentage,
        order: cat.order,
      })),
    };
  }

  static async getDynamicMonthlyTrends(userId: string, months: number = 6): Promise<any[]> {
    const trendsData = [];
    const currentDate = new Date();

    // Get user's categories
    const categories = await Category.find({ userId }).sort({ order: 1 });

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(currentDate);
      date.setMonth(date.getMonth() - i);

      // Set date range for the month
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);

      const dateFilter = {
        userId,
        date: {
          $gte: startOfMonth,
          $lte: endOfMonth
        }
      };

      // Get all transactions for this month
      const transactions = await Transaction.find(dateFilter).populate('category');

      // Calculate totals by category
      const monthData: any = {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
      };

      categories.forEach(category => {
        const categoryTransactions = transactions.filter(
          t => t.categoryId.toString() === category._id.toString()
        );
        const total = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
        monthData[category.name] = total;
      });

      trendsData.push(monthData);
    }

    return trendsData;
  }
}
