import mongoose, { Document, Schema } from 'mongoose';

export interface ICategory extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  displayName: string;
  color: string;
  icon: string;
  percentage: number;
  isDefault: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const categorySchema = new Schema<ICategory>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      match: /^[a-z0-9_]+$/, // Only lowercase letters, numbers, and underscores
    },
    displayName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    color: {
      type: String,
      required: true,
      match: /^#[0-9A-Fa-f]{6}$/, // Hex color format
      default: '#007bff',
    },
    icon: {
      type: String,
      required: true,
      default: 'fas fa-folder',
    },
    percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    order: {
      type: Number,
      required: true,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes for better query performance
categorySchema.index({ userId: 1, name: 1 }, { unique: true });
categorySchema.index({ userId: 1, order: 1 });
categorySchema.index({ userId: 1, isDefault: 1 });

// Pre-save middleware to validate category limits
categorySchema.pre('save', async function(next) {
  if (this.isNew) {
    const maxCategories = parseInt(process.env.MAX_CATEGORIES_PER_USER || '5');
    const CategoryModel = this.constructor as any;
    const existingCount = await CategoryModel.countDocuments({ userId: this.userId });

    if (existingCount >= maxCategories) {
      const error = new Error(`Maximum ${maxCategories} categories allowed per user`);
      return next(error);
    }
  }

  // Validate that total percentages don't exceed 100%
  const CategoryModel = this.constructor as any;
  const totalPercentage = await CategoryModel.aggregate([
    { $match: { userId: this.userId, _id: { $ne: this._id } } },
    { $group: { _id: null, total: { $sum: '$percentage' } } }
  ]);

  const currentTotal = totalPercentage.length > 0 ? totalPercentage[0].total : 0;
  if (currentTotal + this.percentage > 100) {
    const error = new Error(`Total category percentages cannot exceed 100%. Current total: ${currentTotal}%`);
    return next(error);
  }

  next();
});

// Static method to get default categories for new users
categorySchema.statics.getDefaultCategories = function() {
  return [
    {
      name: 'income',
      displayName: 'Income',
      color: '#28a745',
      icon: 'fas fa-money-bill-wave',
      percentage: 0, // Income doesn't count towards budget allocation
      isDefault: true,
      order: 0,
    },
    {
      name: 'needs',
      displayName: 'Needs',
      color: '#dc3545',
      icon: 'fas fa-shopping-basket',
      percentage: 25,
      isDefault: true,
      order: 1,
    },
    {
      name: 'wants',
      displayName: 'Wants',
      color: '#ffc107',
      icon: 'fas fa-gift',
      percentage: 25,
      isDefault: true,
      order: 2,
    },
    {
      name: 'investments',
      displayName: 'Investments',
      color: '#007bff',
      icon: 'fas fa-chart-line',
      percentage: 45,
      isDefault: true,
      order: 3,
    },
    {
      name: 'donations',
      displayName: 'Donations',
      color: '#6f42c1',
      icon: 'fas fa-heart',
      percentage: 5,
      isDefault: true,
      order: 4,
    },
  ];
};

export const Category = mongoose.model<ICategory>('Category', categorySchema);
