import { Router } from 'express';
import { FrontendController } from '../controllers/frontendController';
import {
  checkFrontendAuth,
  requireFrontendAuth,
  redirectIfAuthenticated
} from '../middlewares/frontendAuth';

const router = Router();

// Apply auth check to all routes
router.use(checkFrontendAuth);

// Authentication routes (redirect if already authenticated)
router.get('/login', redirectIfAuthenticated, FrontendController.renderLogin);
router.get('/register', redirectIfAuthenticated, FrontendController.renderRegister);

// Dashboard (requires authentication)
router.get('/dashboard', requireFrontendAuth, FrontendController.renderDashboard);

// Categories page (requires authentication)
router.get('/categories', requireFrontendAuth, FrontendController.renderCategories);

// Profile page (requires authentication)
router.get('/profile', requireFrontendAuth, (req, res) => {
  res.render('pages/profile', {
    title: 'Profile',
    user: req.user,
  });
});

// Transaction routes for each category (requires authentication)
const categories = ['incomes', 'needs', 'wants', 'investments', 'donations'];

categories.forEach(category => {
  // List page
  router.get(`/${category}`, requireFrontendAuth, FrontendController.renderTransactions);

  // New transaction form
  router.get(`/${category}/new`, requireFrontendAuth, FrontendController.renderTransactionForm);

  // Edit transaction form
  router.get(`/${category}/:id/edit`, requireFrontendAuth, FrontendController.renderTransactionForm);
});

// Root redirect
router.get('/', (req: any, res) => {
  if (req.isAuthenticated) {
    res.redirect('/dashboard');
  } else {
    res.redirect('/login');
  }
});

// 404 handler
router.get('*', FrontendController.render404);

export default router;
