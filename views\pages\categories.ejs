<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>Manage Categories
            </h1>
            <button type="button" class="btn btn-primary" id="addCategoryBtn">
                <i class="fas fa-plus me-2"></i>Add Category
            </button>
        </div>
    </div>
</div>

<!-- Category Limit Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            You can create up to <strong id="maxCategories">5</strong> categories. 
            <span id="categoryCount">0</span> of <span id="maxCategoriesText">5</span> categories used.
        </div>
    </div>
</div>

<!-- Categories List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Your Categories
                </h5>
            </div>
            <div class="card-body">
                <div id="categoriesContainer">
                    <!-- Categories will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">Add Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId" name="categoryId">
                    
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="categoryName" name="name" 
                               placeholder="e.g., groceries, entertainment" required
                               pattern="^[a-z0-9_]+$" 
                               title="Only lowercase letters, numbers, and underscores allowed">
                        <div class="form-text">Only lowercase letters, numbers, and underscores allowed</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryDisplayName" class="form-label">Display Name *</label>
                        <input type="text" class="form-control" id="categoryDisplayName" name="displayName" 
                               placeholder="e.g., Groceries, Entertainment" required maxlength="50">
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryColor" class="form-label">Color *</label>
                        <div class="d-flex align-items-center gap-2">
                            <input type="color" class="form-control form-control-color" id="categoryColor" 
                                   name="color" value="#007bff" required style="width: 60px;">
                            <input type="text" class="form-control" id="categoryColorText" 
                                   placeholder="#007bff" pattern="^#[0-9A-Fa-f]{6}$">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryIcon" class="form-label">Icon *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i id="iconPreview" class="fas fa-folder"></i>
                            </span>
                            <input type="text" class="form-control" id="categoryIcon" name="icon" 
                                   value="fas fa-folder" required placeholder="e.g., fas fa-shopping-cart">
                        </div>
                        <div class="form-text">
                            Use FontAwesome icon classes. 
                            <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryPercentage" class="form-label">Budget Percentage *</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="categoryPercentage" name="percentage" 
                                   min="0" max="100" step="0.1" required>
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="form-text">
                            Percentage of income to allocate to this category. 
                            <span id="remainingPercentage">Remaining: 100%</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="categoryForm" class="btn btn-primary" id="saveCategoryBtn">
                    <i class="fas fa-save me-2"></i>Save Category
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category "<span id="deleteCategoryName"></span>"?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone. Categories with existing transactions cannot be deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>Delete Category
                </button>
            </div>
        </div>
    </div>
</div>

<script src="/js/categories.js"></script>
